import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import dayjs from 'dayjs';
import { isObject, omit, pick, uniq } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import type { EventEmailContentSpecsFragment } from '../../../api/fragments/EventApplicationModuleEmailContentSpecs';
import type { EventDataFragment } from '../../../api/fragments/EventData';
import type { UploadFileWithPreviewFormDataFragment } from '../../../api/fragments/UploadFileWithPreviewFormData';
import {
    DeleteEventLevelAssetDocument,
    type DeleteEventLevelAssetMutation,
    type DeleteEventLevelAssetMutationVariables,
} from '../../../api/mutations/deleteEventLevelAsset';
import {
    UpdateEventDocument,
    type UpdateEventMutation,
    type UpdateEventMutationVariables,
} from '../../../api/mutations/updateEvent';
import {
    UploadEventLevelAssetDocument,
    type UploadEventLevelAssetMutation,
    type UploadEventLevelAssetMutationVariables,
} from '../../../api/mutations/uploadEventLevelAsset';
import {
    UpsertKycPresetInEventDocument,
    type UpsertKycPresetInEventMutation,
    type UpsertKycPresetInEventMutationVariables,
} from '../../../api/mutations/upsertKycPresetInEvent';
import { GetEventDocument } from '../../../api/queries/getEvent';
import {
    AspectRatio,
    BannerTextPosition,
    EmailContentUpdateType,
    EventModuleAsset,
    type CustomizedFieldInput,
} from '../../../api/types';
import { useMultipleDealerIds } from '../../../components/contexts/DealerContextManager';
import { downloadImageOrVideoAsFile } from '../../../utilities/downloadImageOrVideoAsFile';
import { isUploadFileWithPreviewFormDataFragment } from '../../../utilities/file';
import { capitalizeFirstChar } from '../../../utilities/fp';
import useHandleError from '../../../utilities/useHandleError';
import useUploadBannerImage from '../BannerDetailsPage/useUploadBannerImage';
import type { AppointmentEmailFormValues } from '../ModuleDetailsPage/AppointmentModulePage/shared';
import { hasAppointmentScenario, hasPaymentScenario } from '../ModuleDetailsPage/modules/implementations/shared';
import {
    getDefaultDealershipPaymentSettingValue,
    processDealershipPaymentSettingInput,
} from '../ModuleDetailsPage/modules/implementations/shared/getDealershipPaymentSetting';
import getKycFieldDefinitionsFromCountry from '../UpdateKYCPresetPage/KYCFieldDefinitions';
import { getDealershipSettingValue } from './EventForm/EventInnerForm';
// eslint-disable-next-line max-len
import { transformCustomTestDriveBookingSlotsForUpdate } from './EventForm/TestDriveBookingSlots/utils/transformTestDriveBookingSlots';
import type { EventFormValues, EventEmailFormValues, CreateEventFormValues } from './EventForm/types';
import { getInitialEventKycField, prepareSubmissionEventKycField } from './EventKycFields/utils';

type UploadParams = {
    id: string;
    upload: File | UploadFileWithPreviewFormDataFragment;
    type: EventModuleAsset;
    forceDownloadAndUpload: boolean;
};

type HandleEventLevelAssetsParams = {
    id: string;
    emailAssets: EventEmailFormValues;
    initialValues?: EventEmailContentSpecsFragment;
    forceDownloadAndUpload?: boolean; // Force download and upload of existing assets
};

const handleForceDownloadAndUpload = async (
    initialFile: File | UploadFileWithPreviewFormDataFragment,
    type: EventModuleAsset
) => {
    let processedFile = initialFile;
    const downloadTasks = [];

    // If forceDownloadAndUpload is true and we have UploadFileWithPreviewFormDataFragment objects,
    // download and convert them to File objects first
    if (isUploadFileWithPreviewFormDataFragment(processedFile) && processedFile.url && processedFile.filename) {
        downloadTasks.push(
            downloadImageOrVideoAsFile(processedFile.url, processedFile.filename).then(file => ({
                type,
                file,
            }))
        );
    }

    if (downloadTasks.length > 0) {
        try {
            const downloadedFiles = await Promise.all(downloadTasks);

            for (const { type, file } of downloadedFiles) {
                if (type === 'introImage') {
                    processedFile = file;
                }
            }
        } catch (error) {
            console.error('Error downloading intro images during submission:', error);
            throw new Error('Failed to process intro images for duplicated event. Please try again.');
        }
    }

    return processedFile;
};

export const useHandleEventLevelAssets = () => {
    const apolloClient = useApolloClient();

    const updateOrDelete = useCallback(
        async (
            { type, id, upload, forceDownloadAndUpload }: UploadParams,
            initialValue: UploadFileWithPreviewFormDataFragment
        ) => {
            let processedFile = upload;

            if (forceDownloadAndUpload) {
                processedFile = await handleForceDownloadAndUpload(processedFile, type);
            }

            const uploads = [];
            if (processedFile instanceof File) {
                // upload a new file
                uploads.push(
                    apolloClient.mutate<UploadEventLevelAssetMutation, UploadEventLevelAssetMutationVariables>({
                        mutation: UploadEventLevelAssetDocument,
                        variables: {
                            id,
                            type,
                            upload: processedFile,
                        },
                    })
                );
            } else if (processedFile === null && isObject(initialValue)) {
                // delete existing file
                uploads.push(
                    apolloClient.mutate<DeleteEventLevelAssetMutation, DeleteEventLevelAssetMutationVariables>({
                        mutation: DeleteEventLevelAssetDocument,
                        variables: { id, type },
                    })
                );
            }

            return uploads;
        },
        [apolloClient]
    );

    return useCallback(
        async ({ id, emailAssets, initialValues, forceDownloadAndUpload = false }: HandleEventLevelAssetsParams) => {
            const baseUploadOrDeleteParams = { id, forceDownloadAndUpload };

            const uploads = [
                ...(await updateOrDelete(
                    {
                        ...baseUploadOrDeleteParams,
                        upload: emailAssets.submitOrder.introImage,
                        type: EventModuleAsset.SubmitOrderIntro,
                    },
                    initialValues?.submitOrder.introImage
                )),
                ...(await updateOrDelete(
                    {
                        ...baseUploadOrDeleteParams,
                        upload: emailAssets.testDrive.customer.bookingAmendment.introImage,
                        type: EventModuleAsset.CustomerBookingAmendment,
                    },
                    initialValues?.testDrive.customer.bookingAmendment.introImage
                )),
                ...(await updateOrDelete(
                    {
                        ...baseUploadOrDeleteParams,
                        upload: emailAssets.testDrive.customer.bookingCancellation.introImage,
                        type: EventModuleAsset.CustomerBookingCancellation,
                    },
                    initialValues?.testDrive.customer.bookingCancellation.introImage
                )),
                ...(await updateOrDelete(
                    {
                        ...baseUploadOrDeleteParams,
                        upload: emailAssets.testDrive.customer.bookingConfirmation.introImage,
                        type: EventModuleAsset.CustomerSubmitConfirmation,
                    },
                    initialValues?.testDrive.customer.bookingConfirmation.introImage
                )),
                ...(await updateOrDelete(
                    {
                        ...baseUploadOrDeleteParams,
                        upload: emailAssets.testDrive.customer.completeTestDriveWithoutProcess.introImage,
                        type: EventModuleAsset.CustomerCompleteTestDriveWithoutProcess,
                    },
                    initialValues?.testDrive.customer.completeTestDriveWithoutProcess.introImage
                )),
                ...(await updateOrDelete(
                    {
                        ...baseUploadOrDeleteParams,
                        upload: emailAssets.testDrive.customer.endTestDriveWithProcess.introImage,
                        type: EventModuleAsset.CustomerEndTestDriveWithProcess,
                    },
                    initialValues?.testDrive.customer.endTestDriveWithProcess.introImage
                )),
                ...(await updateOrDelete(
                    {
                        ...baseUploadOrDeleteParams,
                        upload: emailAssets.testDrive.customer.submitConfirmation.introImage,
                        type: EventModuleAsset.CustomerSubmitConfirmation,
                    },
                    initialValues?.testDrive.customer.submitConfirmation.introImage
                )),
            ];

            if (uploads.length) {
                return Promise.all(uploads);
            }

            return Promise.resolve([]);
        },
        [updateOrDelete]
    );
};

export const useEventUpdateSubmission = (event: EventDataFragment, onFinish?: () => void) => {
    const { dealerOptions } = useMultipleDealerIds();
    const { t } = useTranslation(['eventDetails']);
    const apolloClient = useApolloClient();
    const handleEventLevelAssets = useHandleEventLevelAssets();
    const handleBannerAsset = useUploadBannerImage();

    return useHandleError<EventFormValues>(
        async values => {
            // submitting message
            message.loading({
                content: t('eventDetails:messages.updateSubmitting'),
                key: 'primary',
                duration: 0,
            });

            const {
                publicSalesPerson,
                myInfoSetting,
                paymentSetting,
                dealerVehicles,
                dealerVariantsMapping,
                dealerIds,
                kycPresets,
                customizedFields,
                startTime,
                endTime,
                hasCustomiseEmail,
                hasCustomiseBanner,
                eventLevelEmailSettings,
                isCapEnabled,
                banner,
                excludeVehicleOfInterest,
                ...others
            } = values;

            // change period to company timezone
            const period = {
                start: dayjs(others.period.start)
                    .hour(startTime.hour())
                    .minute(startTime.minute())
                    .second(0)
                    .tz(event.module.company.timeZone, true)
                    .toDate(),
                end: dayjs(others.period.end)
                    .hour(endTime.hour())
                    .minute(endTime.minute())
                    .second(0)
                    .tz(event.module.company.timeZone, true)
                    .toDate(),
            };

            const { introImage, ...submitOrderSetting } = eventLevelEmailSettings.submitOrder;
            const { bannerImage, mobileBannerImage, ...bannerSetting } = banner;

            // get dealers(if any) that user does not belong to
            const otherDealerIds = event.dealerIds.filter(
                dealerId => !dealerOptions.some(option => option.value === dealerId)
            );

            const baseEventInput = omit(['moduleId', 'moduleLevelMobileVerification'], others);
            const transformedCustomTestDriveBookingSlots = transformCustomTestDriveBookingSlotsForUpdate(
                values,
                event.module.company.timeZone
            );

            let updatedEvent = null;

            // submit update main details tab fields
            updatedEvent = await apolloClient.mutate<UpdateEventMutation, UpdateEventMutationVariables>({
                mutation: UpdateEventDocument,
                variables: {
                    id: event.id,
                    eventInput: {
                        ...baseEventInput,
                        ...(!others.privateAccess && { publicSalesPerson }),
                        ...(myInfoSetting?.defaultId && { myInfoSetting }),
                        ...(hasPaymentScenario(others.scenarios) && {
                            paymentSetting: processDealershipPaymentSettingInput(paymentSetting),
                        }),
                        period,
                        dealerIds: uniq([...dealerIds, ...otherDealerIds]),
                        dealerVehicles: values.dealerIds.map(dealerId => ({
                            dealerId,
                            vehicleSuiteIds: dealerVariantsMapping[dealerId],
                        })),
                        customizedFields: customizedFields.map(field => ({
                            ...(omit('index', field) as CustomizedFieldInput),
                        })),
                        hasCustomiseEmail,
                        ...(hasCustomiseEmail && {
                            eventLevelEmailSettings: {
                                emailContentUpdateType: EmailContentUpdateType.Module,
                                submitOrder: {
                                    ...submitOrderSetting,
                                },
                                testDrive: {
                                    customer: {
                                        endTestDriveWithProcess: omit(
                                            ['introImage'],
                                            eventLevelEmailSettings.testDrive.customer.endTestDriveWithProcess
                                        ),
                                        submitConfirmation: omit(
                                            ['introImage'],
                                            eventLevelEmailSettings.testDrive.customer.submitConfirmation
                                        ),
                                        bookingConfirmation: omit(
                                            ['introImage'],
                                            eventLevelEmailSettings.testDrive.customer.bookingConfirmation
                                        ),
                                        bookingCancellation: omit(
                                            ['introImage'],
                                            eventLevelEmailSettings.testDrive.customer.bookingCancellation
                                        ),
                                        bookingAmendment: omit(
                                            ['introImage'],
                                            eventLevelEmailSettings.testDrive.customer.bookingAmendment
                                        ),
                                        completeTestDriveWithoutProcess: omit(
                                            ['introImage'],
                                            eventLevelEmailSettings.testDrive.customer.completeTestDriveWithoutProcess
                                        ),
                                    },
                                },
                            },
                        }),
                        hasCustomiseBanner,
                        ...(hasCustomiseBanner && {
                            banner: {
                                ...bannerSetting,
                            },
                        }),
                        isCapEnabled,
                        hasVehicleIntegration: !excludeVehicleOfInterest,
                        ...(transformedCustomTestDriveBookingSlots !== undefined && {
                            customTestDriveBookingSlots: transformedCustomTestDriveBookingSlots,
                        }),
                    },
                },
            });

            if (hasCustomiseEmail) {
                await handleEventLevelAssets({
                    id: event.id,
                    emailAssets: eventLevelEmailSettings,
                    initialValues: event.emailContents,
                });
            }

            if (hasCustomiseBanner) {
                await handleBannerAsset({
                    bannerId: updatedEvent?.data.event?.banner.id,
                    values: { ...banner, moduleId: event.module.id },
                    initialValues: null,
                    eventId: updatedEvent.data.event.id,
                });
            }

            // submit update for kyc presets
            const kycPresetsData = prepareSubmissionEventKycField(values);

            let index = 1;
            for (const kycPreset of kycPresetsData) {
                // eslint-disable-next-line no-await-in-loop
                await apolloClient.mutate<UpsertKycPresetInEventMutation, UpsertKycPresetInEventMutationVariables>({
                    mutation: UpsertKycPresetInEventDocument,
                    variables: {
                        eventId: event.id,
                        kycPreset,
                    },
                    // Only refetch on the last call
                    refetchQueries: index === kycPresets.length ? [GetEventDocument] : undefined,
                });

                index += 1;
            }

            // inform about success
            message.success({
                content: t('eventDetails:messages.updateSuccessful'),
                key: 'primary',
            });

            // then go back to list
            onFinish?.();
        },
        [
            t,
            event.dealerIds,
            event.module.company.timeZone,
            event.module.id,
            event.id,
            event.emailContents,
            apolloClient,
            onFinish,
            dealerOptions,
            handleBannerAsset,
            handleEventLevelAssets,
        ]
    );
};

export const useEventInitialValues = (event: EventDataFragment) => {
    const { t } = useTranslation('emails');
    const { dealerOptions } = useMultipleDealerIds();

    if (!event) {
        return null;
    }

    return useMemo((): EventFormValues => {
        const { dealerVehicles } = event;
        console.log('event: ', event);
        const kycFields =
            // eslint-disable-next-line no-nested-ternary
            event.module.__typename === 'EventApplicationModule'
                ? event.module.customerModule.__typename === 'LocalCustomerManagementModule'
                    ? event.module.customerModule.kycFields.sort((a, b) => a.order - b.order)
                    : null
                : null;

        const moduleLevelMobileVerification = // eslint-disable-next-line no-nested-ternary
            event.module.__typename === 'EventApplicationModule'
                ? event.module.customerModule.__typename === 'LocalCustomerManagementModule'
                    ? Boolean(event.module.customerModule.extraSettings.mobileVerification)
                    : null
                : null;

        const period = {
            start: dayjs(event.period.start).tz(event.module.company.timeZone),
            end: dayjs(event.period.end).tz(event.module.company.timeZone),
        };

        const definitions = getKycFieldDefinitionsFromCountry(event.module.company.countryCode);

        // use module email contents if event email contents is not set
        const shouldOverwriteWithModuleEmailContents =
            !event.emailContents?.submitOrder ||
            (event.emailContents?.submitOrder?.subject?.defaultValue?.defaultValue === '' &&
                event.emailContents?.submitOrder?.introTitle?.defaultValue?.defaultValue === '' &&
                event.emailContents?.submitOrder?.contentText?.defaultValue?.defaultValue === '' &&
                event.emailContents?.submitOrder?.introImage === null);

        let subjectDefaultValue;
        let subjectOverrides;
        let introTitleDefaultValue;
        let introTitleOverrides;
        let contentTextDefaultValue;
        let contentTextOverrides;
        let introImage;

        if (
            shouldOverwriteWithModuleEmailContents &&
            event.module.__typename === 'EventApplicationModule' &&
            event.module.emailContents?.submitOrder
        ) {
            // Use module email contents when all four conditions are met
            const moduleSubmitOrder = event.module.emailContents.submitOrder;

            subjectDefaultValue = moduleSubmitOrder.defaultValue?.subject?.defaultValue ?? {
                defaultValue: t('event.submitOrder.subject'),
                overrides: [],
            };
            subjectOverrides = moduleSubmitOrder.defaultValue?.subject?.overrides ?? [];

            introTitleDefaultValue = moduleSubmitOrder.defaultValue?.introTitle?.defaultValue ?? {
                defaultValue: t('event.submitOrder.introTitle'),
                overrides: [],
            };
            introTitleOverrides = moduleSubmitOrder.defaultValue?.introTitle?.overrides ?? [];

            contentTextDefaultValue = moduleSubmitOrder.defaultValue?.contentText?.defaultValue ?? {
                defaultValue: t('event.submitOrder.contentText'),
                overrides: [],
            };
            contentTextOverrides = moduleSubmitOrder.defaultValue?.contentText?.overrides ?? [];

            introImage = moduleSubmitOrder.defaultValue?.introImage ?? null;
        } else {
            // Use event email contents or fallback to defaults
            subjectDefaultValue = event.emailContents?.submitOrder?.subject?.defaultValue ?? {
                defaultValue: t('event.submitOrder.subject'),
                overrides: [],
            };
            subjectOverrides = event.emailContents?.submitOrder?.subject?.overrides ?? [];

            introTitleDefaultValue = event.emailContents?.submitOrder?.introTitle?.defaultValue ?? {
                defaultValue: t('event.submitOrder.introTitle'),
                overrides: [],
            };
            introTitleOverrides = event.emailContents?.submitOrder?.introTitle?.overrides ?? [];

            contentTextDefaultValue = event.emailContents?.submitOrder?.contentText?.defaultValue ?? {
                defaultValue: t('event.submitOrder.contentText'),
                overrides: [],
            };
            contentTextOverrides = event.emailContents?.submitOrder?.contentText?.overrides ?? [];

            introImage = event.emailContents?.submitOrder?.introImage ?? null;
        }

        const testDriveCustomerEmailContents = event.emailContents?.testDrive?.customer;

        const getTestDriveCustomerEmailContents = (
            value: AppointmentEmailFormValues['customer'],
            key: keyof AppointmentEmailFormValues['customer']
        ) => ({
            subject: {
                defaultValue: {
                    defaultValue:
                        value?.[key]?.subject?.defaultValue?.defaultValue ??
                        t(`emails:appointment.customer${capitalizeFirstChar(key)}.subject`),
                    overrides: value?.[key]?.subject?.defaultValue?.overrides ?? [],
                },
                overrides: value?.[key]?.subject?.overrides ?? [],
            },
            introTitle: {
                defaultValue: {
                    defaultValue:
                        value?.[key]?.introTitle?.defaultValue?.defaultValue ??
                        t(`emails:appointment.customer${capitalizeFirstChar(key)}.introTitle`),
                    overrides: value?.[key]?.introTitle?.defaultValue?.overrides ?? [],
                },
                overrides: value?.[key]?.introTitle?.overrides ?? [],
            },
            contentText: {
                defaultValue: {
                    defaultValue:
                        value?.[key]?.contentText?.defaultValue?.defaultValue ??
                        t(`emails:appointment.customer${capitalizeFirstChar(key)}.contentText`),
                    overrides: value?.[key]?.contentText?.defaultValue?.overrides ?? [],
                },
                overrides: value?.[key]?.contentText?.overrides ?? [],
            },
            isSummaryVehicleVisible: {
                defaultValue: !!value?.[key]?.isSummaryVehicleVisible?.defaultValue,
                overrides: value?.[key]?.isSummaryVehicleVisible?.overrides ?? [],
            },
            introImage: value?.[key]?.introImage ?? null,
        });

        const initialEmailSettings: EventEmailFormValues = {
            submitOrder: {
                subject: {
                    defaultValue: subjectDefaultValue,
                    overrides: subjectOverrides,
                },
                introTitle: {
                    defaultValue: introTitleDefaultValue,
                    overrides: introTitleOverrides,
                },
                contentText: {
                    defaultValue: contentTextDefaultValue,
                    overrides: contentTextOverrides,
                },
                introImage,
            },
            testDrive: {
                customer: {
                    submitConfirmation: getTestDriveCustomerEmailContents(
                        testDriveCustomerEmailContents,
                        'submitConfirmation'
                    ),
                    endTestDriveWithProcess: getTestDriveCustomerEmailContents(
                        testDriveCustomerEmailContents,
                        'endTestDriveWithProcess'
                    ),
                    completeTestDriveWithoutProcess: getTestDriveCustomerEmailContents(
                        testDriveCustomerEmailContents,
                        'completeTestDriveWithoutProcess'
                    ),
                    bookingAmendment: getTestDriveCustomerEmailContents(
                        testDriveCustomerEmailContents,
                        'bookingAmendment'
                    ),
                    bookingConfirmation: getTestDriveCustomerEmailContents(
                        testDriveCustomerEmailContents,
                        'bookingConfirmation'
                    ),
                    bookingCancellation: getTestDriveCustomerEmailContents(
                        testDriveCustomerEmailContents,
                        'bookingCancellation'
                    ),
                },
            },
        };

        return {
            ...pick(
                [
                    'displayName',
                    'name',
                    'isActive',
                    'privateAccess',
                    'isAllowTradeIn',
                    'scenarios',
                    'urlSlug',
                    'dealerVehicles',
                    'showLiveChat',
                    'showDealership',
                    'customizedFields',
                    'skipForDeposit',
                    'isCapEnabled',
                    'isSearchCapCustomerOptional',
                    'capPrequalification',
                    'enableDynamicUtmTracking',
                    'utmParametersSettings',
                    'isCustomerDataRetreivalByPorscheId',
                    'isPorscheIdLoginMandatory',
                    'displayAppointmentDatepicker',
                    'displayVisitAppointmentDatepicker',
                    'depositAmount',
                    'kycExtraSettings',
                    'salesConsultantAutoAssignmentEnabled',
                    'hasCustomiseThankYouPage',
                    'thankYouPageContent',
                ],
                event
            ),
            customTestDriveBookingSlots: (() => {
                if (event.customTestDriveBookingSlots) {
                    return {
                        ...event.customTestDriveBookingSlots,
                        fixedPeriods:
                            event.customTestDriveBookingSlots.fixedPeriods?.map(period => ({
                                id: period._id,
                                dateRange:
                                    period.startDate && period.endDate
                                        ? {
                                              start: dayjs(period.startDate).tz(event.module.company.timeZone),
                                              end: dayjs(period.endDate).tz(event.module.company.timeZone),
                                          }
                                        : undefined,
                                startDate: dayjs(period.startDate).format('DD MM YYYY'),
                                endDate: dayjs(period.endDate).format('DD MM YYYY'),
                                bookingTimeSlot: period.bookingTimeSlot.map(item => ({
                                    ...item,
                                    // Use current date instead of date from saved data
                                    // as initial value. To accommodate DST changes
                                    // So it matches the label of the field also
                                    slot: dayjs()
                                        .set('hour', dayjs(item.slot).hour())
                                        .set('minute', dayjs(item.slot).minute())
                                        .set('second', 0)
                                        .tz(event.module.company.timeZone),
                                })),
                                advancedBookingLimit: period.advancedBookingLimit,
                            })) || [],
                        bookingWindowSettings: event.customTestDriveBookingSlots.bookingWindowSettings
                            ? {
                                  ...event.customTestDriveBookingSlots.bookingWindowSettings,
                                  bookingTimeSlot:
                                      event.customTestDriveBookingSlots.bookingWindowSettings.bookingTimeSlot.map(
                                          item => ({
                                              ...item,
                                              // Use current date instead of date from saved data
                                              // as initial value. To accommodate DST changes
                                              // So it matches the label of the field also
                                              slot: dayjs()
                                                  .set('hour', dayjs(item.slot).hour())
                                                  .set('minute', dayjs(item.slot).minute())
                                                  .set('second', 0)
                                                  .tz(event.module.company.timeZone),
                                          })
                                      ),
                              }
                            : undefined,
                    };
                }

                // Default structure when displayAppointmentDatepicker is true but no custom slots exist
                if (event.displayAppointmentDatepicker) {
                    return {
                        isEnabled: false,
                        bookingPeriodType: undefined, // Let user choose the type
                        fixedPeriods: [],
                    };
                }

                return undefined;
            })(),
            dealerIds: event.dealerIds.filter(dealerId => dealerOptions.some(option => option.value === dealerId)),
            isAllowTestDrive: hasAppointmentScenario(event.scenarios),
            publicSalesPerson: getDealershipSettingValue(event.publicSalesPerson, event.dealerIds),
            myInfoSetting: event.myInfoSetting ?? {
                defaultId: null,
                overrides: [],
            },
            paymentSetting: getDefaultDealershipPaymentSettingValue(event.paymentSetting),
            dealerVariantsMapping: Object.fromEntries(
                dealerVehicles.map(({ dealerId, vehicleSuiteIds }) => [dealerId, vehicleSuiteIds])
            ),
            kycPresets: getInitialEventKycField(definitions, event, kycFields),
            period: {
                start: dayjs().year(period.start.year()).month(period.start.month()).date(period.start.date()),
                end: dayjs().year(period.end.year()).month(period.end.month()).date(period.end.date()),
            },
            startTime: period.start,
            endTime: period.end,
            hasCustomiseEmail: event.hasCustomiseEmail ?? false,
            hasCustomiseBanner: event.hasCustomiseBanner ?? false,
            eventLevelEmailSettings: initialEmailSettings,
            moduleId: event.module?.id ?? '',
            banner: {
                bannerHeader: event?.banner?.bannerHeader ?? {
                    defaultValue: '',
                    overrides: [],
                },
                bannerText: event?.banner?.bannerText ?? {
                    defaultValue: '',
                    overrides: [],
                },
                isActive: event?.banner?.isActive ?? false,
                bannerTextPosition: event?.banner?.bannerTextPosition ?? BannerTextPosition.Left,
                bannerImage: event?.banner?.bannerImage ?? null,
                mobileBannerImage: event?.banner?.mobileBannerImage ?? null,
                mobileRatio: event?.banner?.mobileRatio ?? AspectRatio.Square,
            },
            excludeVehicleOfInterest: !event.hasVehicleIntegration,
            moduleLevelMobileVerification,
            ownerId: event.owner?.id,
        };
    }, [event, t, dealerOptions]);
};

export const useDuplicateEventInitialValues = (event?: EventDataFragment): CreateEventFormValues | null => {
    const initialValues = useEventInitialValues(event);

    if (!initialValues) {
        return null;
    }

    return useMemo(
        () => ({
            ...omit(['moduleLevelMobileVerification'], initialValues),
            moduleId: initialValues.moduleId ?? '',
            name: {
                ...initialValues.name,
                defaultValue: `${initialValues.name.defaultValue}_copy`,
            },
            displayName: `${initialValues.displayName}_copy`,
            urlSlug: `${initialValues.urlSlug}_copy`,
        }),
        [initialValues]
    );
};
